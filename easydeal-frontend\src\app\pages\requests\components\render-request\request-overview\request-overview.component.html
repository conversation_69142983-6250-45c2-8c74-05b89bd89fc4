<div class="row mb-7">
  <label class="col-lg-4 fw-bold text-muted">Request Operation Type</label>
  <div class="col-lg-8">
    <span class="fw-bolder fs-6 text-gray-900">{{ request?.type }}</span>
  </div>
</div>

<div class="row mb-7">
  <label class="col-lg-4 fw-bold text-muted">Specialization Scope</label>
  <div class="col-lg-8">
    <span class="fw-bolder fs-6 text-gray-900">{{ request?.specializationScope }}</span>
  </div>
</div>

<div class="row mb-7">
  <label class="col-lg-4 fw-bold text-muted">Unit Type</label>
  <div class="col-lg-8">
    <span class="fw-bolder fs-6 text-gray-900">{{ request?.unit }}</span>
  </div>
</div>

<div class="row mb-7">
  <label class="col-lg-4 fw-bold text-muted">Created At</label>
  <div class="col-lg-8">
    <span class="fw-bolder fs-6 text-gray-900">{{ request?.createdAt }}</span>
  </div>
</div>

<!-- Dynamic Location Info -->
<div *ngFor="let cityItem of request?.locations">
  <div class="row mb-7">
    <label class="col-lg-4 fw-bold text-muted">City</label>
    <div class="col-lg-8">
      <span class="fw-bolder fs-6 text-gray-900">{{ cityItem.city.name_en }} | {{ cityItem.city.name_ar }}</span>
    </div>
  </div>

  <div *ngFor="let areaItem of cityItem.areas">
    <div class="row mb-7">
      <label class="col-lg-4 fw-bold text-muted">Area</label>
      <div class="col-lg-8">
        <span class="fw-bolder fs-6 text-gray-900">{{ areaItem.area.name_en }} | {{ areaItem.area.name_ar }}</span>
      </div>
    </div>

    <div *ngFor="let subarea of areaItem.sub_areas">
      <div class="row mb-7">
        <label class="col-lg-4 fw-bold text-muted">Subarea</label>
        <div class="col-lg-8">
          <span class="fw-bolder fs-6 text-gray-900">{{ subarea.name_en }} | {{ subarea.name_ar }}</span>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="row mb-7" *ngIf="request?.detailedAddress || request?.attributes?.detailedAddress">
  <label class="col-lg-4 fw-bold text-muted">Detailed Address</label>
  <div class="col-lg-8">
    <span class="fw-bolder fs-6 text-gray-900">{{ request?.detailedAddress || request?.attributes?.detailedAddress
      }}</span>
  </div>
</div>

<div class="row mb-7" *ngIf="request?.attributes?.addressLink">
  <label class="col-lg-4 fw-bold text-muted">Address Link</label>
  <div class="col-lg-8">
    <a [href]="request.attributes.addressLink" target="_blank" rel="noopener noreferrer"
      class="fw-bold fs-6 text-gray-900 text-hover-primary">{{ request.attributes.addressLink }}</a>
  </div>
</div>

<div class="row mb-7" *ngIf="request?.attributes?.buildingArea">
  <label class="col-lg-4 fw-bold text-muted">Building Area</label>
  <div class="col-lg-8">
    <span class="fw-bolder fs-6 text-gray-900">{{ request?.attributes?.buildingArea | number:'1.0-2' }} m²</span>
  </div>
</div>

<div class="row mb-7" *ngIf="request?.attributes?.groundArea">
  <label class="col-lg-4 fw-bold text-muted">Ground Area</label>
  <div class="col-lg-8">
    <span class="fw-bolder fs-6 text-gray-900">{{ request?.attributes?.groundArea | number:'1.0-2' }} m²</span>
  </div>
</div>

<div class="row mb-7" *ngIf="request?.attributes?.buildingDeadline">
  <label class="col-lg-4 fw-bold text-muted">Building Deadline</label>
  <div class="col-lg-8">
    <span class="fw-bolder fs-6 text-gray-900">{{ request?.attributes?.buildingDeadline }}</span>
  </div>
</div>

<div class="row mb-7" *ngIf="request?.attributes?.buildingLayoutStatus">
  <label class="col-lg-4 fw-bold text-muted">Building Layout Status</label>
  <div class="col-lg-8">
    <span class="fw-bolder fs-6 text-gray-900">{{ request?.attributes?.buildingLayoutStatus }}</span>
  </div>
</div>

<div class="row mb-7" *ngIf="request?.attributes?.buildingLicense">
  <label class="col-lg-4 fw-bold text-muted">Building License</label>
  <div class="col-lg-8">
    <span class="fw-bolder fs-6 text-gray-900">{{ request?.attributes?.buildingLicense }}</span>
  </div>
</div>

<div class="row mb-7" *ngIf="request?.attributes?.deliveryStatus">
  <label class="col-lg-4 fw-bold text-muted">Delivery Status</label>
  <div class="col-lg-8">
    <span class="fw-bolder fs-6 text-gray-900">{{ request?.attributes?.deliveryStatus }}</span>
  </div>
</div>

<div class="row mb-7" *ngIf="request?.attributes?.financialStatus">
  <label class="col-lg-4 fw-bold text-muted">Financial Status</label>
  <div class="col-lg-8">
    <span class="fw-bolder fs-6 text-gray-900">{{ request?.attributes?.financialStatus }}</span>
  </div>
</div>

<div class="row mb-7" *ngIf="request?.attributes?.mallName">
  <label class="col-lg-4 fw-bold text-muted">Mall Name</label>
  <div class="col-lg-8">
    <span class="fw-bolder fs-6 text-gray-900">{{ request?.attributes?.mallName }}</span>
  </div>
</div>

<div class="row mb-7" *ngIf="request?.attributes?.notes">
  <label class="col-lg-4 fw-bold text-muted">Notes</label>
  <div class="col-lg-8">
    <span class="fw-bolder fs-6 text-gray-900">{{ request?.attributes?.notes }}</span>
  </div>
</div>

<div class="row mb-7" *ngIf="request?.attributes?.paymentMethod">
  <label class="col-lg-4 fw-bold text-muted">Payment Method</label>
  <div class="col-lg-8">
    <span class="fw-bolder fs-6 text-gray-900">{{ request?.attributes?.paymentMethod }}</span>
  </div>
</div>

<div class="row mb-7" *ngIf="request?.attributes?.unitDescription">
  <label class="col-lg-4 fw-bold text-muted">Unit Description</label>
  <div class="col-lg-8">
    <span class="fw-bolder fs-6 text-gray-900">{{ request?.attributes?.unitDescription }}</span>
  </div>
</div>

<div class="row mb-7" *ngIf="request?.attributes?.unitNumber">
  <label class="col-lg-4 fw-bold text-muted">Unit Number</label>
  <div class="col-lg-8">
    <span class="fw-bolder fs-6 text-gray-900">{{ request?.attributes?.unitNumber }}</span>
  </div>
</div>

<div class="row mb-7" *ngIf="request?.attributes?.unitPrice">
  <label class="col-lg-4 fw-bold text-muted">Unit Price</label>
  <div class="col-lg-8">
    <span class="fw-bolder fs-6 text-gray-900">{{ request?.attributes?.unitPrice }}</span>
  </div>
</div>

<div class="row mb-7" *ngIf="request?.attributes?.unitPriceSuggestions">
  <label class="col-lg-4 fw-bold text-muted">Unit Price Suggestion</label>
  <div class="col-lg-8">
    <span class="fw-bolder fs-6 badge badge-light-danger" [ngClass]="{
        'badge-light-success': request?.attributes?.unitPriceSuggestions === 1,
        'badge-light-danger': request?.attributes?.unitPriceSuggestions !== 1
      }">
      {{ (request?.attributes?.unitPriceSuggestions === 1 ? 'TRUE' : 'FALSE') }}
    </span>
  </div>
</div>

<div class="row mb-7" *ngIf="request?.attributes?.unitView">
  <label class="col-lg-4 fw-bold text-muted">Unit View</label>
  <div class="col-lg-8">
    <span class="fw-bolder fs-6 text-gray-900">{{ request?.attributes?.unitView }}</span>
  </div>
</div>

<div class="row mb-7" *ngIf="request?.attributes?.areaSuggestions">
  <label class="col-lg-4 fw-bold text-muted">Area Suggestion</label>
  <div class="col-lg-8">
    <span class="fw-bolder fs-6 text-gray-900">{{ request?.attributes?.areaSuggestions }}</span>
  </div>
</div>

<div class="row mb-7" *ngIf="request?.attributes?.unitAreaMax">
  <label class="col-lg-4 fw-bold text-muted">Area</label>
  <div class="col-lg-8">
    <span class="fw-bolder fs-6 text-gray-900">{{ request?.attributes?.unitAreaMin | number:'1.0-2' }} : {{
      request?.attributes?.unitAreaMax | number:'1.0-2' }} m²</span>
  </div>
</div>

<div class="row mb-7" *ngIf="request?.attributes?.averageUnitPriceMax">
  <label class="col-lg-4 fw-bold text-muted">Average Unit Price</label>
  <div class="col-lg-8">
    <span class="fw-bolder fs-6 text-gray-900">{{ request?.attributes?.averageUnitPriceMin }} : {{
      request?.attributes?.averageUnitPriceMax }} EGP</span>
  </div>
</div>

<div class="row mb-7" *ngIf="request?.attributes?.bathRooms">
  <label class="col-lg-4 fw-bold text-muted">Bathrooms</label>
  <div class="col-lg-8">
    <span class="fw-bolder fs-6 text-gray-900">{{ request?.attributes?.bathRooms }}</span>
  </div>
</div>

<div class="row mb-7" *ngIf="request?.attributes?.floor">
  <label class="col-lg-4 fw-bold text-muted">Floor</label>
  <div class="col-lg-8">
    <span class="fw-bolder fs-6 text-gray-900">{{ request?.attributes?.floor }}</span>
  </div>
</div>

<div *ngIf="request?.attributes.otherAccessories">
  <div class="row mb-7">
    <label class="col-lg-4 fw-bold text-muted">Other Accessories</label>
    <div class="col-md-8">
      <div *ngFor="let item of request?.attributes.otherAccessories" class="d-inline-block">
        <span class="fw-bolder fs-6 text-gray-900 me-1 pe-1 border-dark border-end border-2">{{ item }} </span>
      </div>
    </div>
  </div>
</div>

<div class="row mb-7" *ngIf="request?.attributes?.rooms">
  <label class="col-lg-4 fw-bold text-muted">Rooms</label>
  <div class="col-lg-8">
    <span class="fw-bolder fs-6 text-gray-900">{{ request?.attributes?.rooms }}</span>
  </div>
</div>

<div class="row mb-7" *ngIf="request?.attributes?.finishingStatus">
  <label class="col-lg-4 fw-bold text-muted">Finishing Status</label>
  <div class="col-lg-8">
    <span class="fw-bolder fs-6 text-gray-900">{{ request?.attributes?.finishingStatus }}</span>
  </div>
</div>

<!-- Additional Fields with Conditional Display -->

<div class="row mb-7" *ngIf="request?.attributes?.compoundName">
  <label class="col-lg-4 fw-bold text-muted">Compound Name</label>
  <div class="col-lg-8">
    <span class="fw-bolder fs-6 text-gray-900">{{ request?.attributes?.compoundName }}</span>
  </div>
</div>

<div class="row mb-7" *ngIf="request?.attributes?.projectConstructor">
  <label class="col-lg-4 fw-bold text-muted">Project Constructor</label>
  <div class="col-lg-8">
    <span class="fw-bolder fs-6 text-gray-900">{{ request?.attributes?.projectConstructor }}</span>
  </div>
</div>

<div class="row mb-7" *ngIf="request?.attributes?.projectManagement">
  <label class="col-lg-4 fw-bold text-muted">Project Management</label>
  <div class="col-lg-8">
    <span class="fw-bolder fs-6 text-gray-900">{{ request?.attributes?.projectManagement }}</span>
  </div>
</div>

<div class="row mb-7" *ngIf="request?.attributes?.parkingStatus">
  <label class="col-lg-4 fw-bold text-muted">Parking Status</label>
  <div class="col-lg-8">
    <span class="fw-bolder fs-6 text-gray-900">{{ request?.attributes?.parkingStatus }}</span>
  </div>
</div>

<div class="row mb-7" *ngIf="request?.attributes?.clubStatus">
  <label class="col-lg-4 fw-bold text-muted">Club Status</label>
  <div class="col-lg-8">
    <span class="fw-bolder fs-6 text-gray-900">{{ request?.attributes?.clubStatus }}</span>
  </div>
</div>

<div class="row mb-7" *ngIf="request?.attributes?.askingPrice">
  <label class="col-lg-4 fw-bold text-muted">Asking Price</label>
  <div class="col-lg-8">
    <span class="fw-bolder fs-6 text-gray-900">{{ request?.attributes?.askingPrice | number:'1.0-2' }} EGP</span>
  </div>
</div>

<div class="row mb-7" *ngIf="request?.attributes?.priceSuggestion">
  <label class="col-lg-4 fw-bold text-muted">Price Suggestion</label>
  <div class="col-lg-8">
    <span class="fw-bolder fs-6 text-gray-900">{{ request?.attributes?.priceSuggestion | number:'1.0-2' }} EGP</span>
  </div>
</div>

<div class="row mb-7" *ngIf="request?.attributes?.requestedOver">
  <label class="col-lg-4 fw-bold text-muted">Requested Over</label>
  <div class="col-lg-8">
    <span class="fw-bolder fs-6 text-gray-900">{{ request?.attributes?.requestedOver }}</span>
  </div>
</div>

<div class="row mb-7" *ngIf="request?.attributes?.buildingNumber">
  <label class="col-lg-4 fw-bold text-muted">Building Number</label>
  <div class="col-lg-8">
    <span class="fw-bolder fs-6 text-gray-900">{{ request?.attributes?.buildingNumber }}</span>
  </div>
</div>

<div class="row mb-7" *ngIf="request?.attributes?.unitArea">
  <label class="col-lg-4 fw-bold text-muted">Unit Area</label>
  <div class="col-lg-8">
    <span class="fw-bolder fs-6 text-gray-900">{{ request?.attributes?.unitArea | number:'1.0-2' }} m²</span>
  </div>
</div>

<div class="row mb-7" *ngIf="request?.attributes?.gardenArea">
  <label class="col-lg-4 fw-bold text-muted">Garden Area</label>
  <div class="col-lg-8">
    <span class="fw-bolder fs-6 text-gray-900">{{ request?.attributes?.gardenArea | number:'1.0-2' }} m²</span>
  </div>
</div>

<div class="row mb-7" *ngIf="request?.attributes?.terraceArea">
  <label class="col-lg-4 fw-bold text-muted">Terrace Area</label>
  <div class="col-lg-8">
    <span class="fw-bolder fs-6 text-gray-900">{{ request?.attributes?.terraceArea | number:'1.0-2' }} m²</span>
  </div>
</div>

<div class="row mb-7" *ngIf="request?.attributes?.numberOfFloors">
  <label class="col-lg-4 fw-bold text-muted">Number of Floors</label>
  <div class="col-lg-8">
    <span class="fw-bolder fs-6 text-gray-900">{{ request?.attributes?.numberOfFloors }}</span>
  </div>
</div>

<div class="row mb-7" *ngIf="request?.attributes?.locationSuggestions">
  <label class="col-lg-4 fw-bold text-muted">Location Suggestions</label>
  <div class="col-lg-8">
    <span class="fw-bolder fs-6 text-gray-900">{{ request?.attributes?.locationSuggestions }}</span>
  </div>
</div>

<div class="row mb-7" *ngIf="request?.attributes?.averageUnitPriceSuggestions">
  <label class="col-lg-4 fw-bold text-muted">Average Unit Price Suggestions</label>
  <div class="col-lg-8">
    <span class="fw-bolder fs-6 text-gray-900">{{ request?.attributes?.averageUnitPriceSuggestions | number:'1.0-2' }}
      EGP</span>
  </div>
</div>

<div class="row mb-7" *ngIf="request?.attributes?.unitAreaMin && request?.attributes?.unitAreaMax">
  <label class="col-lg-4 fw-bold text-muted">Unit Area Range</label>
  <div class="col-lg-8">
    <span class="fw-bolder fs-6 text-gray-900">{{ request?.attributes?.unitAreaMin | number:'1.0-2' }} : {{
      request?.attributes?.unitAreaMax | number:'1.0-2' }} m²</span>
  </div>
</div>

<div class="row mb-7" *ngIf="request?.attributes?.buildingAreaMin && request?.attributes?.buildingAreaMax">
  <label class="col-lg-4 fw-bold text-muted">Building Area Range</label>
  <div class="col-lg-8">
    <span class="fw-bolder fs-6 text-gray-900">{{ request?.attributes?.buildingAreaMin | number:'1.0-2' }} : {{
      request?.attributes?.buildingAreaMax | number:'1.0-2' }} m²</span>
  </div>
</div>

<div class="row mb-7" *ngIf="request?.attributes?.buildingAreaSuggestions">
  <label class="col-lg-4 fw-bold text-muted">Building Area Suggestions</label>
  <div class="col-lg-8">
    <span class="fw-bolder fs-6 text-gray-900">{{ request?.attributes?.buildingAreaSuggestions | number:'1.0-2' }}
      m²</span>
  </div>
</div>

<div class="row mb-7" *ngIf="request?.attributes?.fitOutCondition">
  <label class="col-lg-4 fw-bold text-muted">Fit Out Condition</label>
  <div class="col-lg-8">
    <span class="fw-bolder fs-6 text-gray-900">{{ request?.attributes?.fitOutCondition }}</span>
  </div>
</div>

<div class="row mb-7" *ngIf="request?.attributes?.activity">
  <label class="col-lg-4 fw-bold text-muted">Activity</label>
  <div class="col-lg-8">
    <span class="fw-bolder fs-6 text-gray-900">{{ request?.attributes?.activity }}</span>
  </div>
</div>

<div class="row mb-7" *ngIf="request?.attributes?.unitLayoutStatus">
  <label class="col-lg-4 fw-bold text-muted">Unit Layout Status</label>
  <div class="col-lg-8">
    <span class="fw-bolder fs-6 text-gray-900">{{ request?.attributes?.unitLayoutStatus }}</span>
  </div>
</div>

<div class="row mb-7" *ngIf="request?.attributes?.unitDesign">
  <label class="col-lg-4 fw-bold text-muted">Unit Design</label>
  <div class="col-lg-8">
    <span class="fw-bolder fs-6 text-gray-900">{{ request?.attributes?.unitDesign }}</span>
  </div>
</div>

<div class="row mb-7" *ngIf="request?.attributes?.groundLayoutStatus">
  <label class="col-lg-4 fw-bold text-muted">Ground Layout Status</label>
  <div class="col-lg-8">
    <span class="fw-bolder fs-6 text-gray-900">{{ request?.attributes?.groundLayoutStatus }}</span>
  </div>
</div>

<div class="row mb-7" *ngIf="request?.attributes?.groundAreaMin && request?.attributes?.groundAreaMax">
  <label class="col-lg-4 fw-bold text-muted">Ground Area Range</label>
  <div class="col-lg-8">
    <span class="fw-bolder fs-6 text-gray-900">{{ request?.attributes?.groundAreaMin | number:'1.0-2' }} : {{
      request?.attributes?.groundAreaMax | number:'1.0-2' }} m²</span>
  </div>
</div>

<div class="row mb-7" *ngIf="request?.attributes?.favoriteFloor">
  <label class="col-lg-4 fw-bold text-muted">Favorite Floor</label>
  <div class="col-lg-8">
    <span class="fw-bolder fs-6 text-gray-900">{{ request?.attributes?.favoriteFloor }}</span>
  </div>
</div>

<div class="row mb-7" *ngIf="request?.attributes?.furnishingStatus">
  <label class="col-lg-4 fw-bold text-muted">Furnishing Status</label>
  <div class="col-lg-8">
    <span class="fw-bolder fs-6 text-gray-900">{{ request?.attributes?.furnishingStatus }}</span>
  </div>
</div>

<div class="row mb-7" *ngIf="request?.attributes?.rentRecurrence">
  <label class="col-lg-4 fw-bold text-muted">Rent Recurrence</label>
  <div class="col-lg-8">
    <span class="fw-bolder fs-6 text-gray-900">{{ request?.attributes?.rentRecurrence }}</span>
  </div>
</div>

<div class="row mb-7" *ngIf="request?.attributes?.villageName">
  <label class="col-lg-4 fw-bold text-muted">Village Name</label>
  <div class="col-lg-8">
    <span class="fw-bolder fs-6 text-gray-900">{{ request?.attributes?.villageName }}</span>
  </div>
</div>

<div class="row mb-7" *ngIf="request?.attributes?.subUnitType">
  <label class="col-lg-4 fw-bold text-muted">Sub Unit Type</label>
  <div class="col-lg-8">
    <span class="fw-bolder fs-6 text-gray-900">{{ request?.attributes?.subUnitType }}</span>
  </div>
</div>

<div class="row mb-7" *ngIf="request?.attributes?.rentDuration">
  <label class="col-lg-4 fw-bold text-muted">Rent Duration</label>
  <div class="col-lg-8">
    <span class="fw-bolder fs-6 text-gray-900">{{ request?.attributes?.rentDuration }}</span>
  </div>
</div>

<div class="row mb-7" *ngIf="request?.attributes?.rentDateMin && request?.attributes?.rentDateMax">
  <label class="col-lg-4 fw-bold text-muted">Rent Date Range</label>
  <div class="col-lg-8">
    <span class="fw-bolder fs-6 text-gray-900">{{ request?.attributes?.rentDateMin }} : {{
      request?.attributes?.rentDateMax }}</span>
  </div>
</div>

<div class="row mb-7"
  *ngIf="request?.attributes?.averageUnitPriceMonthlyMin && request?.attributes?.averageUnitPriceMonthlyMax">
  <label class="col-lg-4 fw-bold text-muted">Average Unit Price Monthly Range</label>
  <div class="col-lg-8">
    <span class="fw-bolder fs-6 text-gray-900">{{ request?.attributes?.averageUnitPriceMonthlyMin | number:'1.0-2' }} :
      {{ request?.attributes?.averageUnitPriceMonthlyMax | number:'1.0-2' }} EGP</span>
  </div>
</div>

<div class="row mb-7" *ngIf="request?.attributes?.averageUnitPriceMonthlySuggestions">
  <label class="col-lg-4 fw-bold text-muted">Average Unit Price Monthly Suggestions</label>
  <div class="col-lg-8">
    <span class="fw-bolder fs-6 text-gray-900">{{ request?.attributes?.averageUnitPriceMonthlySuggestions |
      number:'1.0-2' }} EGP</span>
  </div>
</div>

<div class="row mb-7"
  *ngIf="request?.attributes?.averageUnitPriceDailyMin && request?.attributes?.averageUnitPriceDailyMax">
  <label class="col-lg-4 fw-bold text-muted">Average Unit Price Daily Range</label>
  <div class="col-lg-8">
    <span class="fw-bolder fs-6 text-gray-900">{{ request?.attributes?.averageUnitPriceDailyMin | number:'1.0-2' }} : {{
      request?.attributes?.averageUnitPriceDailyMax | number:'1.0-2' }} EGP</span>
  </div>
</div>

<div class="row mb-7" *ngIf="request?.attributes?.averageUnitPriceDailySuggestions">
  <label class="col-lg-4 fw-bold text-muted">Average Unit Price Daily Suggestions</label>
  <div class="col-lg-8">
    <span class="fw-bolder fs-6 text-gray-900">{{ request?.attributes?.averageUnitPriceDailySuggestions | number:'1.0-2'
      }} EGP</span>
  </div>
</div>

<div class="row mb-7" *ngIf="request?.attributes?.unitFacing">
  <label class="col-lg-4 fw-bold text-muted">Unit Facing</label>
  <div class="col-lg-8">
    <span class="fw-bolder fs-6 text-gray-900">{{ request?.attributes?.unitFacing }}</span>
  </div>
</div>
