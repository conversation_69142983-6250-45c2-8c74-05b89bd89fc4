{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"./auth.service\";\nimport * as i2 from \"src/app/pages/authentication\";\nimport * as i3 from \"@angular/router\";\nexport class AuthGuard {\n  authService;\n  authenticationService;\n  router;\n  constructor(authService, authenticationService, router) {\n    this.authService = authService;\n    this.authenticationService = authenticationService;\n    this.router = router;\n  }\n  canActivate(route, state) {\n    const currentUser = localStorage.getItem('authToken');\n    if (currentUser) {\n      // logged in so return true\n      return true;\n    }\n    // not logged in so redirect to login page with the return url\n    // Clear any existing auth data\n    localStorage.removeItem('authToken');\n    localStorage.removeItem('currentUser');\n    this.authService.currentUserValue = undefined;\n    return this.router.createUrlTree(['/authentication/login']);\n  }\n  static ɵfac = function AuthGuard_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || AuthGuard)(i0.ɵɵinject(i1.AuthService), i0.ɵɵinject(i2.AuthenticationService), i0.ɵɵinject(i3.Router));\n  };\n  static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: AuthGuard,\n    factory: AuthGuard.ɵfac,\n    providedIn: 'root'\n  });\n}\nexport class NoAuthGuard {\n  router;\n  constructor(router) {\n    this.router = router;\n  }\n  canActivate(route, state) {\n    const authToken = localStorage.getItem('authToken');\n    const currentUser = localStorage.getItem('currentUser');\n    if (authToken && currentUser) {\n      // User is logged in, redirect to profile\n      return this.router.createUrlTree(['/profile']);\n    }\n    // User is not logged in, allow access to auth pages\n    return true;\n  }\n  static ɵfac = function NoAuthGuard_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NoAuthGuard)(i0.ɵɵinject(i3.Router));\n  };\n  static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: NoAuthGuard,\n    factory: NoAuthGuard.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "authService", "authenticationService", "router", "constructor", "canActivate", "route", "state", "currentUser", "localStorage", "getItem", "removeItem", "currentUserValue", "undefined", "createUrlTree", "i0", "ɵɵinject", "i1", "AuthService", "i2", "AuthenticationService", "i3", "Router", "factory", "ɵfac", "providedIn", "NoAuth<PERSON><PERSON>", "authToken"], "sources": ["C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\modules\\auth\\services\\auth.guard.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { ActivatedRouteSnapshot, Router, RouterStateSnapshot } from '@angular/router';\r\nimport { AuthService } from './auth.service';\r\nimport { AuthenticationService } from 'src/app/pages/authentication';\r\n\r\n@Injectable({ providedIn: 'root' })\r\nexport class AuthGuard  {\r\n  constructor(private authService: AuthService, private authenticationService: AuthenticationService, private router: Router) {}\r\n\r\n  canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) {\r\n    const currentUser = localStorage.getItem('authToken');\r\n    if (currentUser) {\r\n      // logged in so return true\r\n      return true;\r\n    }\r\n\r\n    // not logged in so redirect to login page with the return url\r\n    // Clear any existing auth data\r\n    localStorage.removeItem('authToken');\r\n    localStorage.removeItem('currentUser');\r\n    this.authService.currentUserValue = undefined;\r\n    return this.router.createUrlTree(['/authentication/login']);\r\n  }\r\n}\r\n\r\n@Injectable({ providedIn: 'root' })\r\nexport class NoAuthGuard {\r\n  constructor(private router: Router) {}\r\n\r\n  canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) {\r\n    const authToken = localStorage.getItem('authToken');\r\n    const currentUser = localStorage.getItem('currentUser');\r\n\r\n    if (authToken && currentUser) {\r\n      // User is logged in, redirect to profile\r\n      return this.router.createUrlTree(['/profile']);\r\n    }\r\n\r\n    // User is not logged in, allow access to auth pages\r\n    return true;\r\n  }\r\n}\r\n"], "mappings": ";;;;AAMA,OAAM,MAAOA,SAAS;EACAC,WAAA;EAAkCC,qBAAA;EAAsDC,MAAA;EAA5GC,YAAoBH,WAAwB,EAAUC,qBAA4C,EAAUC,MAAc;IAAtG,KAAAF,WAAW,GAAXA,WAAW;IAAuB,KAAAC,qBAAqB,GAArBA,qBAAqB;IAAiC,KAAAC,MAAM,GAANA,MAAM;EAAW;EAE7HE,WAAWA,CAACC,KAA6B,EAAEC,KAA0B;IACnE,MAAMC,WAAW,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;IACrD,IAAIF,WAAW,EAAE;MACf;MACA,OAAO,IAAI;IACb;IAEA;IACA;IACAC,YAAY,CAACE,UAAU,CAAC,WAAW,CAAC;IACpCF,YAAY,CAACE,UAAU,CAAC,aAAa,CAAC;IACtC,IAAI,CAACV,WAAW,CAACW,gBAAgB,GAAGC,SAAS;IAC7C,OAAO,IAAI,CAACV,MAAM,CAACW,aAAa,CAAC,CAAC,uBAAuB,CAAC,CAAC;EAC7D;;qCAhBWd,SAAS,EAAAe,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,qBAAA,GAAAL,EAAA,CAAAC,QAAA,CAAAK,EAAA,CAAAC,MAAA;EAAA;;WAATtB,SAAS;IAAAuB,OAAA,EAATvB,SAAS,CAAAwB,IAAA;IAAAC,UAAA,EADI;EAAM;;AAqBhC,OAAM,MAAOC,WAAW;EACFvB,MAAA;EAApBC,YAAoBD,MAAc;IAAd,KAAAA,MAAM,GAANA,MAAM;EAAW;EAErCE,WAAWA,CAACC,KAA6B,EAAEC,KAA0B;IACnE,MAAMoB,SAAS,GAAGlB,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;IACnD,MAAMF,WAAW,GAAGC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;IAEvD,IAAIiB,SAAS,IAAInB,WAAW,EAAE;MAC5B;MACA,OAAO,IAAI,CAACL,MAAM,CAACW,aAAa,CAAC,CAAC,UAAU,CAAC,CAAC;IAChD;IAEA;IACA,OAAO,IAAI;EACb;;qCAdWY,WAAW,EAAAX,EAAA,CAAAC,QAAA,CAAAK,EAAA,CAAAC,MAAA;EAAA;;WAAXI,WAAW;IAAAH,OAAA,EAAXG,WAAW,CAAAF,IAAA;IAAAC,UAAA,EADE;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}