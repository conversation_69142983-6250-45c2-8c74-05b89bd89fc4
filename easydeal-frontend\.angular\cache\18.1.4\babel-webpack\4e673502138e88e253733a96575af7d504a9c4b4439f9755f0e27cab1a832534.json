{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { AuthenticationLayoutComponent } from './authentication-layout.component';\nimport { LoginComponent } from './components/login/login.component';\nimport { RegisterComponent } from './components/register/register.component';\nimport { NoAuthGuard } from 'src/app/modules/auth/services/auth.guard';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: AuthenticationLayoutComponent,\n  canActivate: [NoAuthGuard],\n  children: [{\n    path: '',\n    redirectTo: 'login',\n    pathMatch: 'full'\n  }, {\n    path: 'login',\n    component: LoginComponent\n  }, {\n    path: 'register',\n    component: RegisterComponent\n  }]\n}];\nexport class AuthenticationRoutingModule {\n  static ɵfac = function AuthenticationRoutingModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || AuthenticationRoutingModule)();\n  };\n  static ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: AuthenticationRoutingModule\n  });\n  static ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [RouterModule.forChild(routes), RouterModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AuthenticationRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "AuthenticationLayoutComponent", "LoginComponent", "RegisterComponent", "NoAuth<PERSON><PERSON>", "routes", "path", "component", "canActivate", "children", "redirectTo", "pathMatch", "AuthenticationRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\authentication\\authentication-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { AuthenticationLayoutComponent } from './authentication-layout.component';\r\nimport { LoginComponent } from './components/login/login.component';\r\nimport { RegisterComponent } from './components/register/register.component';\r\nimport { NoAuthGuard } from 'src/app/modules/auth/services/auth.guard';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: '',\r\n    component: AuthenticationLayoutComponent,\r\n    canActivate: [NoAuthGuard],\r\n    children: [\r\n      {\r\n        path: '',\r\n        redirectTo: 'login',\r\n        pathMatch: 'full',\r\n      },\r\n      {\r\n        path: 'login',\r\n        component: LoginComponent,\r\n      },\r\n      {\r\n        path: 'register',\r\n        component: RegisterComponent,\r\n      },\r\n    ],\r\n  },\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule],\r\n})\r\nexport class AuthenticationRoutingModule {}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,6BAA6B,QAAQ,mCAAmC;AACjF,SAASC,cAAc,QAAQ,oCAAoC;AACnE,SAASC,iBAAiB,QAAQ,0CAA0C;AAC5E,SAASC,WAAW,QAAQ,0CAA0C;;;AAEtE,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEN,6BAA6B;EACxCO,WAAW,EAAE,CAACJ,WAAW,CAAC;EAC1BK,QAAQ,EAAE,CACR;IACEH,IAAI,EAAE,EAAE;IACRI,UAAU,EAAE,OAAO;IACnBC,SAAS,EAAE;GACZ,EACD;IACEL,IAAI,EAAE,OAAO;IACbC,SAAS,EAAEL;GACZ,EACD;IACEI,IAAI,EAAE,UAAU;IAChBC,SAAS,EAAEJ;GACZ;CAEJ,CACF;AAMD,OAAM,MAAOS,2BAA2B;;qCAA3BA,2BAA2B;EAAA;;UAA3BA;EAA2B;;cAH5BZ,YAAY,CAACa,QAAQ,CAACR,MAAM,CAAC,EAC7BL,YAAY;EAAA;;;2EAEXY,2BAA2B;IAAAE,OAAA,GAAAC,EAAA,CAAAf,YAAA;IAAAgB,OAAA,GAF5BhB,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}